#!/usr/bin/env python3
"""
爬虫池管理服务
"""

import hashlib
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
import logging

from ..models import (
    CrawlerAccount, Platform, ApiCallLog, ProxyPool,
    CrawlerSession, CrawlerPoolSchedule, CrawlerRateLimit,
    CrawlerPerformanceMetrics, AntiCrawlDetection
)
from .alert_service import alert_service, AlertLevel

logger = logging.getLogger(__name__)

class CrawlerPoolService:
    """爬虫池管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
        
        # 配置参数
        self.session_timeout = 3600  # 会话超时时间(秒)
        self.max_sessions_per_ip = 3  # 每个IP最大会话数
        self.max_requests_per_minute = 60  # 每分钟最大请求数
        self.cooling_period = 300  # 冷却期(秒)
        self.health_threshold = 50.0  # 健康度阈值
        
    def get_or_create_session(self, client_ip: str, user_agent: str, 
                            platform_code: str) -> Optional[str]:
        """获取或创建会话"""
        try:
            # 生成会话ID
            session_data = f"{client_ip}:{user_agent}:{platform_code}:{int(time.time())}"
            session_id = hashlib.md5(session_data.encode()).hexdigest()
            
            # 检查是否存在活跃会话
            existing_session = self.db.query(CrawlerSession).filter(
                and_(
                    CrawlerSession.client_ip == client_ip,
                    CrawlerSession.platform_code == platform_code,
                    CrawlerSession.status == 'active',
                    CrawlerSession.expires_at > datetime.now()
                )
            ).first()
            
            if existing_session:
                # 更新现有会话
                existing_session.last_request_at = datetime.now()
                existing_session.request_count += 1
                self.db.commit()
                return existing_session.session_id
            
            # 检查IP会话数限制
            ip_session_count = self.db.query(CrawlerSession).filter(
                and_(
                    CrawlerSession.client_ip == client_ip,
                    CrawlerSession.status == 'active',
                    CrawlerSession.expires_at > datetime.now()
                )
            ).count()
            
            if ip_session_count >= self.max_sessions_per_ip:
                logger.warning(f"IP {client_ip} 会话数超限: {ip_session_count}")
                return None
            
            # 创建新会话
            new_session = CrawlerSession(
                session_id=session_id,
                client_ip=client_ip,
                user_agent=user_agent,
                platform_code=platform_code,
                status='active',
                expires_at=datetime.now() + timedelta(seconds=self.session_timeout),
                last_request_at=datetime.now()
            )
            
            self.db.add(new_session)
            self.db.commit()
            
            logger.info(f"创建新会话: {session_id} for IP {client_ip}")
            return session_id
            
        except Exception as e:
            logger.error(f"获取或创建会话失败: {str(e)}")
            self.db.rollback()
            return None
    
    def select_best_crawler(self, platform_code: str, session_id: str = None,
                          client_ip: str = None) -> Optional[CrawlerAccount]:
        """选择最佳爬虫账号"""
        try:
            # 获取平台信息
            platform = self.db.query(Platform).filter(Platform.code == platform_code).first()
            if not platform:
                logger.error(f"平台不存在: {platform_code}")
                return None
            
            # 检查频率限制
            if not self._check_rate_limit(client_ip, platform_code):
                logger.warning(f"IP {client_ip} 触发频率限制")
                return None
            
            # 获取可用的爬虫账号
            available_crawlers = self._get_available_crawlers(platform.id)
            
            if not available_crawlers:
                logger.warning(f"没有可用的{platform_code}爬虫账号")
                # 发送告警
                asyncio.create_task(self._send_no_crawler_alert(platform_code, platform.name))
                return None
            
            # 选择最佳爬虫
            best_crawler = self._select_optimal_crawler(available_crawlers, session_id)
            
            if best_crawler:
                # 绑定会话和爬虫
                self._bind_session_crawler(session_id, best_crawler.id)
                
                # 更新调度状态
                self._update_crawler_schedule(best_crawler.id, platform_code)
                
                logger.info(f"选择爬虫: {best_crawler.username} for session {session_id}")
            
            return best_crawler
            
        except Exception as e:
            logger.error(f"选择最佳爬虫失败: {str(e)}")
            return None
    
    def _get_available_crawlers(self, platform_id: int) -> List[CrawlerAccount]:
        """获取可用的爬虫账号"""
        try:
            # 基础查询条件
            query = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == platform_id,
                    CrawlerAccount.status == 'active',
                    CrawlerAccount.is_enabled == True,
                    CrawlerAccount.login_status == 'logged_in'
                )
            )
            
            # 获取调度信息
            available_accounts = []
            accounts = query.all()
            
            for account in accounts:
                schedule = self.db.query(CrawlerPoolSchedule).filter(
                    and_(
                        CrawlerPoolSchedule.account_id == account.id,
                        CrawlerPoolSchedule.status.in_(['available', 'busy'])
                    )
                ).first()
                
                # 检查是否可用
                if self._is_crawler_available(account, schedule):
                    available_accounts.append(account)
            
            return available_accounts
            
        except Exception as e:
            logger.error(f"获取可用爬虫失败: {str(e)}")
            return []
    
    def _is_crawler_available(self, account: CrawlerAccount, 
                            schedule: CrawlerPoolSchedule = None) -> bool:
        """检查爬虫是否可用"""
        try:
            now = datetime.now()
            
            # 检查账号健康度
            if account.success_rate < self.health_threshold:
                return False
            
            # 检查错误次数
            if account.error_count >= 3:
                return False
            
            # 检查Token是否过期
            if account.token_expires_at and account.token_expires_at <= now:
                return False
            
            # 检查调度状态
            if schedule:
                # 检查调度状态是否可用
                if schedule.status not in ['available', 'busy']:
                    return False

                # 检查是否在冷却期
                if schedule.cooling_until and schedule.cooling_until > now:
                    return False

                # 检查是否被阻塞
                if schedule.blocked_until and schedule.blocked_until > now:
                    return False

                # 检查并发限制
                if schedule.current_sessions >= schedule.max_concurrent_sessions:
                    return False

                # 检查请求频率
                if schedule.requests_per_minute >= schedule.max_requests_per_minute:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查爬虫可用性失败: {str(e)}")
            return False
    
    def _select_optimal_crawler(self, crawlers: List[CrawlerAccount], 
                              session_id: str = None) -> Optional[CrawlerAccount]:
        """选择最优爬虫账号"""
        try:
            scored_crawlers = []
            
            for crawler in crawlers:
                score = self._calculate_crawler_score(crawler)
                scored_crawlers.append((crawler, score))
            
            # 按分数排序
            scored_crawlers.sort(key=lambda x: x[1], reverse=True)
            
            # 添加随机性，避免总是选择同一个账号
            import random
            top_crawlers = scored_crawlers[:min(3, len(scored_crawlers))]
            
            if top_crawlers:
                # 加权随机选择
                weights = [score for _, score in top_crawlers]
                selected = random.choices(top_crawlers, weights=weights, k=1)[0]
                return selected[0]
            
            return None
            
        except Exception as e:
            logger.error(f"选择最优爬虫失败: {str(e)}")
            return None
    
    def _calculate_crawler_score(self, crawler: CrawlerAccount) -> float:
        """计算爬虫账号评分"""
        try:
            score = 0.0
            
            # 成功率权重 (40%)
            success_rate = float(crawler.success_rate or 0)
            score += success_rate * 0.4
            
            # 健康度权重 (30%)
            health_score = 100.0 - min(float(crawler.error_count or 0) * 10, 50)
            score += health_score * 0.3
            
            # 负载权重 (20%) - 优先选择负载较低的账号
            schedule = self.db.query(CrawlerPoolSchedule).filter(
                CrawlerPoolSchedule.account_id == crawler.id
            ).first()
            
            if schedule:
                load_factor = float(schedule.load_factor or 0)
                load_score = (1.0 - load_factor) * 100
                score += load_score * 0.2
            else:
                score += 100 * 0.2  # 新账号获得满分
            
            # 最后使用时间权重 (10%) - 优先选择最近未使用的账号
            if crawler.last_used_at:
                hours_since_last_use = (datetime.now() - crawler.last_used_at).total_seconds() / 3600
                freshness_score = min(hours_since_last_use * 10, 100)
                score += freshness_score * 0.1
            else:
                score += 100 * 0.1  # 从未使用过的账号获得满分
            
            return max(score, 0.0)
            
        except Exception as e:
            logger.error(f"计算爬虫评分失败: {str(e)}")
            return 0.0
    
    def _check_rate_limit(self, client_ip: str, platform_code: str) -> bool:
        """检查频率限制"""
        try:
            if not client_ip:
                return True
            
            now = datetime.now()
            window_start = now - timedelta(minutes=1)
            
            # 检查IP级别的频率限制
            rate_limit = self.db.query(CrawlerRateLimit).filter(
                and_(
                    CrawlerRateLimit.identifier == client_ip,
                    CrawlerRateLimit.identifier_type == 'ip',
                    CrawlerRateLimit.platform_code == platform_code
                )
            ).first()
            
            if not rate_limit:
                # 创建新的频率限制记录
                rate_limit = CrawlerRateLimit(
                    identifier=client_ip,
                    identifier_type='ip',
                    platform_code=platform_code,
                    window_start_at=window_start,
                    window_end_at=now,
                    current_requests=1
                )
                self.db.add(rate_limit)
                self.db.commit()
                return True
            
            # 检查是否在阻塞期
            if rate_limit.blocked_until and rate_limit.blocked_until > now:
                return False
            
            # 检查时间窗口
            if rate_limit.window_end_at < window_start:
                # 重置窗口
                rate_limit.window_start_at = window_start
                rate_limit.window_end_at = now
                rate_limit.current_requests = 1
            else:
                # 增加请求计数
                rate_limit.current_requests += 1
            
            # 检查是否超限
            if rate_limit.current_requests > rate_limit.max_requests:
                # 记录违规
                rate_limit.violation_count += 1
                rate_limit.last_violation_at = now
                
                # 设置阻塞时间
                block_duration = min(rate_limit.violation_count * 60, 3600)  # 最多阻塞1小时
                rate_limit.blocked_until = now + timedelta(seconds=block_duration)
                
                self.db.commit()
                return False
            
            self.db.commit()
            return True
            
        except Exception as e:
            logger.error(f"检查频率限制失败: {str(e)}")
            return True  # 出错时允许通过
    
    def _bind_session_crawler(self, session_id: str, account_id: int):
        """绑定会话和爬虫账号"""
        try:
            if not session_id:
                return
            
            session = self.db.query(CrawlerSession).filter(
                CrawlerSession.session_id == session_id
            ).first()
            
            if session:
                session.account_id = account_id
                session.updated_at = datetime.now()
                self.db.commit()
                
        except Exception as e:
            logger.error(f"绑定会话和爬虫失败: {str(e)}")
    
    def _update_crawler_schedule(self, account_id: int, platform_code: str):
        """更新爬虫调度状态"""
        try:
            schedule = self.db.query(CrawlerPoolSchedule).filter(
                and_(
                    CrawlerPoolSchedule.account_id == account_id,
                    CrawlerPoolSchedule.platform_code == platform_code
                )
            ).first()
            
            if not schedule:
                # 创建新的调度记录
                schedule = CrawlerPoolSchedule(
                    account_id=account_id,
                    platform_code=platform_code,
                    status='busy',
                    current_sessions=1,
                    requests_per_minute=1,
                    last_used_at=datetime.now()
                )
                self.db.add(schedule)
            else:
                # 更新现有调度记录
                schedule.status = 'busy'
                schedule.current_sessions += 1
                schedule.requests_per_minute += 1
                schedule.last_used_at = datetime.now()
                
                # 计算负载因子
                load_factor = schedule.current_sessions / schedule.max_concurrent_sessions
                schedule.load_factor = min(load_factor, 1.0)
            
            self.db.commit()
            
        except Exception as e:
            logger.error(f"更新爬虫调度状态失败: {str(e)}")
    
    def record_request_result(self, session_id: str, account_id: int, 
                            success: bool, response_time_ms: int = None,
                            error_message: str = None):
        """记录请求结果"""
        try:
            # 更新会话统计
            if session_id:
                session = self.db.query(CrawlerSession).filter(
                    CrawlerSession.session_id == session_id
                ).first()
                
                if session:
                    if success:
                        session.success_count += 1
                    else:
                        session.error_count += 1
                        session.risk_score += 5.0  # 增加风险评分
                    
                    session.last_request_at = datetime.now()
            
            # 更新账号统计
            if account_id:
                account = self.db.query(CrawlerAccount).filter(
                    CrawlerAccount.id == account_id
                ).first()
                
                if account:
                    account.total_requests += 1
                    account.last_used_at = datetime.now()
                    
                    if success:
                        account.success_requests += 1
                        account.error_count = max(0, account.error_count - 1)  # 成功时减少错误计数
                    else:
                        account.error_count += 1
                    
                    # 重新计算成功率
                    if account.total_requests > 0:
                        account.success_rate = (account.success_requests / account.total_requests) * 100

                        # 检查健康度告警
                        if (account.success_rate < self.health_threshold and
                            account.total_requests >= 10):  # 至少10次请求后才告警
                            platform_name = account.platform.name if account.platform else "未知平台"
                            asyncio.create_task(self._send_crawler_health_alert(account, platform_name))

            self.db.commit()
            
        except Exception as e:
            logger.error(f"记录请求结果失败: {str(e)}")
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            now = datetime.now()
            
            # 标记过期会话
            expired_count = self.db.query(CrawlerSession).filter(
                and_(
                    CrawlerSession.status == 'active',
                    CrawlerSession.expires_at <= now
                )
            ).update({'status': 'expired'})
            
            # 重置调度状态
            self.db.query(CrawlerPoolSchedule).filter(
                CrawlerPoolSchedule.current_sessions > 0
            ).update({
                'current_sessions': 0,
                'status': 'available'
            })
            
            self.db.commit()
            
            if expired_count > 0:
                logger.info(f"清理了 {expired_count} 个过期会话")
                
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
    
    def get_pool_status(self, platform_code: str = None) -> Dict:
        """获取爬虫池状态"""
        try:
            query = self.db.query(CrawlerPoolSchedule)
            if platform_code:
                query = query.filter(CrawlerPoolSchedule.platform_code == platform_code)
            
            schedules = query.all()
            
            status = {
                'total_crawlers': len(schedules),
                'available': len([s for s in schedules if s.status == 'available']),
                'busy': len([s for s in schedules if s.status == 'busy']),
                'cooling': len([s for s in schedules if s.status == 'cooling']),
                'blocked': len([s for s in schedules if s.status == 'blocked']),
                'maintenance': len([s for s in schedules if s.status == 'maintenance']),
                'avg_health_score': sum(s.health_score for s in schedules) / len(schedules) if schedules else 0,
                'total_sessions': sum(s.current_sessions for s in schedules),
                'total_requests_per_minute': sum(s.requests_per_minute for s in schedules)
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取爬虫池状态失败: {str(e)}")
            return {}

    async def _send_no_crawler_alert(self, platform_code: str, platform_name: str):
        """发送无可用爬虫告警"""
        try:
            # 获取平台统计信息
            total_accounts = self.db.query(CrawlerAccount).filter(
                CrawlerAccount.platform_id == self.db.query(Platform).filter(
                    Platform.code == platform_code
                ).first().id
            ).count()

            active_accounts = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == self.db.query(Platform).filter(
                        Platform.code == platform_code
                    ).first().id,
                    CrawlerAccount.status == 'active',
                    CrawlerAccount.is_enabled == True
                )
            ).count()

            logged_in_accounts = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == self.db.query(Platform).filter(
                        Platform.code == platform_code
                    ).first().id,
                    CrawlerAccount.login_status == 'logged_in'
                )
            ).count()

            # 获取问题账号信息
            problem_accounts = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == self.db.query(Platform).filter(
                        Platform.code == platform_code
                    ).first().id,
                    or_(
                        CrawlerAccount.status != 'active',
                        CrawlerAccount.is_enabled == False,
                        CrawlerAccount.login_status != 'logged_in',
                        CrawlerAccount.success_rate < self.health_threshold
                    )
                )
            ).all()

            crawler_accounts = []
            for account in problem_accounts:
                status_text = "未知"
                status_class = "error"

                if not account.is_enabled:
                    status_text = "已禁用"
                elif account.status != 'active':
                    status_text = f"状态异常({account.status})"
                elif account.login_status != 'logged_in':
                    status_text = f"登录失效({account.login_status})"
                elif account.success_rate < self.health_threshold:
                    status_text = f"成功率过低({account.success_rate:.1f}%)"
                    status_class = "warning"

                crawler_accounts.append({
                    'platform': platform_code,
                    'platform_name': platform_name,
                    'username': account.username,
                    'status': account.status,
                    'status_text': status_text,
                    'status_class': status_class,
                    'updated_at': account.updated_at.strftime('%Y-%m-%d %H:%M:%S') if account.updated_at else '',
                    'remark': f"成功率: {account.success_rate:.1f}%, 错误次数: {account.error_count}"
                })

            statistics = [
                {'name': '总账号数', 'value': str(total_accounts), 'unit': '个'},
                {'name': '启用账号数', 'value': str(active_accounts), 'unit': '个'},
                {'name': '已登录账号数', 'value': str(logged_in_accounts), 'unit': '个'},
                {'name': '可用账号数', 'value': '0', 'unit': '个', 'change': -logged_in_accounts}
            ]

            next_actions = [
                "检查账号登录状态，重新登录失效账号",
                "启用被禁用的账号",
                "检查账号密码是否正确",
                "刷新过期的Token",
                "添加新的爬虫账号"
            ]

            await alert_service.send_crawler_alert(
                title=f"{platform_name}平台无可用爬虫账号",
                message=f"检测到{platform_name}平台当前没有可用的爬虫账号，这将影响该平台的数据抓取功能。",
                level=AlertLevel.ERROR,
                crawler_accounts=crawler_accounts,
                statistics=statistics,
                next_actions=next_actions
            )

        except Exception as e:
            logger.error(f"发送无可用爬虫告警失败: {e}")

    async def _send_crawler_health_alert(self, account: CrawlerAccount, platform_name: str):
        """发送爬虫健康度告警"""
        try:
            crawler_accounts = [{
                'platform': account.platform.code if account.platform else 'unknown',
                'platform_name': platform_name,
                'username': account.username,
                'status': account.status,
                'status_text': f"成功率过低({account.success_rate:.1f}%)",
                'status_class': 'warning',
                'updated_at': account.updated_at.strftime('%Y-%m-%d %H:%M:%S') if account.updated_at else '',
                'remark': f"错误次数: {account.error_count}, 总请求: {account.total_requests}"
            }]

            statistics = [
                {'name': '当前成功率', 'value': f"{account.success_rate:.1f}", 'unit': '%'},
                {'name': '健康阈值', 'value': f"{self.health_threshold:.1f}", 'unit': '%'},
                {'name': '错误次数', 'value': str(account.error_count), 'unit': '次'},
                {'name': '总请求数', 'value': str(account.total_requests), 'unit': '次'}
            ]

            next_actions = [
                "检查账号是否被平台限制",
                "重新登录账号刷新状态",
                "暂时禁用该账号避免进一步恶化",
                "分析错误日志找出问题原因"
            ]

            await alert_service.send_crawler_alert(
                title=f"爬虫账号健康度告警",
                message=f"{platform_name}平台爬虫账号 {account.username} 成功率过低，需要关注。",
                level=AlertLevel.WARNING,
                crawler_accounts=crawler_accounts,
                statistics=statistics,
                next_actions=next_actions
            )

        except Exception as e:
            logger.error(f"发送爬虫健康度告警失败: {e}")
