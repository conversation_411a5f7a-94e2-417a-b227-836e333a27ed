"""
SQL日志工具模块
提供统一的SQL语句打印和调试功能，支持开发/生产环境区分
"""

import os
import logging
from typing import Optional
from sqlalchemy import event
from sqlalchemy.engine import Engine
from sqlalchemy.pool import Pool
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class SQLLogger:
    """SQL日志记录器"""

    def __init__(self):
        # 优先使用运行时环境变量，其次使用.env文件配置
        # 这样启动脚本设置的环境变量会覆盖.env文件
        self.environment = os.getenv("ENVIRONMENT", "development").lower()
        self.sql_debug = os.getenv("SQL_DEBUG", "true").lower() == "true"
        self.sql_log_level = os.getenv("SQL_LOG_LEVEL", "INFO").upper()
        
        # 设置日志记录器
        self.logger = logging.getLogger("sqlalchemy.engine")
        self.setup_logger()
        
        # 是否启用SQL打印
        self.enabled = self.is_sql_debug_enabled()
    
    def setup_logger(self):
        """设置SQL日志记录器"""
        if not self.logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            
            # 设置日志格式
            formatter = logging.Formatter(
                '[SQL] %(asctime)s [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(console_handler)
            
            # 设置日志级别
            level = getattr(logging, self.sql_log_level, logging.INFO)
            self.logger.setLevel(level)
            console_handler.setLevel(level)
    
    def is_sql_debug_enabled(self) -> bool:
        """检查是否启用SQL调试"""
        # 开发环境且SQL_DEBUG为true时启用
        if self.environment == "development" and self.sql_debug:
            return True
        
        # 生产环境默认禁用，除非明确设置
        if self.environment == "production":
            # 生产环境只有明确设置SQL_DEBUG=true才启用
            return self.sql_debug
        
        return False
    
    def get_echo_setting(self) -> bool:
        """获取SQLAlchemy echo设置"""
        return self.enabled
    
    def log_sql(self, statement: str, parameters: Optional[dict] = None, 
                execution_time: Optional[float] = None):
        """记录SQL语句"""
        if not self.enabled:
            return
        
        # 格式化SQL语句
        formatted_sql = self.format_sql(statement)
        
        # 记录SQL语句
        self.logger.info(f"执行SQL: {formatted_sql}")
        
        # 记录参数
        if parameters:
            self.logger.info(f"参数: {parameters}")
        
        # 记录执行时间
        if execution_time is not None:
            self.logger.info(f"执行时间: {execution_time:.3f}ms")
    
    def format_sql(self, statement: str) -> str:
        """格式化SQL语句"""
        # 移除多余的空白字符
        formatted = " ".join(statement.split())
        
        # 限制长度，避免日志过长
        max_length = 500
        if len(formatted) > max_length:
            formatted = formatted[:max_length] + "..."
        
        return formatted
    
    def setup_sqlalchemy_logging(self, engine: Engine):
        """为SQLAlchemy引擎设置日志监听器"""
        if not self.enabled:
            return
        
        @event.listens_for(engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """SQL执行前的监听器"""
            context._query_start_time = datetime.now()
        
        @event.listens_for(engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """SQL执行后的监听器"""
            if hasattr(context, '_query_start_time'):
                execution_time = (datetime.now() - context._query_start_time).total_seconds() * 1000
                self.log_sql(statement, parameters, execution_time)
    
    def log_connection_info(self, url: str):
        """记录数据库连接信息"""
        if self.enabled:
            # 隐藏密码信息
            safe_url = self.mask_password(url)
            self.logger.info(f"数据库连接: {safe_url}")
            self.logger.info(f"环境: {self.environment}")
            self.logger.info(f"SQL调试: {'启用' if self.enabled else '禁用'}")
    
    def mask_password(self, url: str) -> str:
        """隐藏URL中的密码"""
        import re
        # 匹配 ://用户名:密码@主机 的模式
        pattern = r'(://[^:]+:)[^@]+(@)'
        return re.sub(pattern, r'\1****\2', url)


# 全局SQL日志记录器实例
sql_logger = SQLLogger()


def get_sql_logger() -> SQLLogger:
    """获取SQL日志记录器实例"""
    return sql_logger


def setup_sql_logging(engine: Engine):
    """为引擎设置SQL日志记录"""
    sql_logger.setup_sqlalchemy_logging(engine)


def is_sql_debug_enabled() -> bool:
    """检查是否启用SQL调试"""
    return sql_logger.is_sql_debug_enabled()


def get_echo_setting() -> bool:
    """获取SQLAlchemy echo设置"""
    return sql_logger.get_echo_setting()


def log_database_connection(url: str):
    """记录数据库连接信息"""
    sql_logger.log_connection_info(url)
