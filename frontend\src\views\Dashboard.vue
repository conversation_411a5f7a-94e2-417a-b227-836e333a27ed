<template>
  <div class="dashboard">
    <div class="page-header">
      <h2></h2>
      <div class="header-actions">
        <button @click="refreshData" class="btn-secondary">🔄 刷新</button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <h3>最近请求</h3>
        <div class="stat-value">{{ stats.recent_requests }}</div>
        <div class="stat-suffix">次</div>
      </div>
      <div class="stat-card">
        <h3>成功率</h3>
        <div class="stat-value">{{ stats.success_rate }}</div>
        <div class="stat-suffix">%</div>
      </div>
      <div class="stat-card">
        <h3>活跃账号</h3>
        <div class="stat-value">{{ stats.active_accounts }}</div>
        <div class="stat-suffix">个</div>
      </div>
      <div class="stat-card">
        <h3>总平台数</h3>
        <div class="stat-value">{{ stats.total_platforms }}</div>
        <div class="stat-suffix">个</div>
      </div>
    </div>

        <!-- 支持的平台 -->
    <div class="platforms-card">
      <h3>支持的平台</h3>
      <div class="platforms-grid">
        <div v-for="platform in platforms" :key="platform.code" class="platform-card">
          <div class="platform-name">{{ platform.name }}</div>
          <div class="platform-status" :class="platform.status">
            {{ platform.status === 'active' ? '已支持' : '开发中' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索测试 -->
    <div class="search-card">
      <h3>搜索测试</h3>

      <div class="search-form">
        <div class="form-item">
          <label>平台:</label>
          <select v-model="searchForm.platform">
            <option v-for="option in platformOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="form-item">
          <label>关键词:</label>
          <input
            v-model="searchForm.query"
            type="text"
            placeholder="请输入搜索关键词"
          />
        </div>
        <div class="form-item">
          <label>语言:</label>
          <select v-model="searchForm.language">
            <option v-for="option in languageOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="form-item">
          <button @click="handleSearch" :disabled="searching" class="search-btn">
            {{ searching ? '搜索中...' : '搜索' }}
          </button>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <h4>搜索结果</h4>
        <div class="products-grid">
          <div v-for="product in searchResults" :key="product.id || product.title" class="product-card">
            <div class="product-image">
              <img
                :src="product.picUrl || product.image || 'https://via.placeholder.com/150x150'"
                :alt="product.name || product.title"
                @error="handleImageError"
                loading="lazy"
              />
            </div>
            <div class="product-info">
              <div class="product-title" :title="product.name || product.title">{{ product.name || product.title }}</div>
              <div class="product-price">
                <span v-if="product.price && typeof product.price === 'number'">
                  ¥{{ (product.price / 100).toFixed(2) }}
                  <span v-if="product.marketPrice && product.marketPrice !== product.price" class="market-price">
                    ¥{{ (product.marketPrice / 100).toFixed(2) }}
                  </span>
                </span>
                <span v-else>{{ product.price }}</span>
              </div>
              <div class="product-details">
                <div class="product-sales" v-if="product.salesCount || product.sales">
                  销量: {{ product.salesCount || product.sales }}
                </div>
                <div class="product-location" v-if="product.location">{{ product.location }}</div>
                <div class="product-shop" v-if="product.shopName || product.shop_name">
                  店铺: {{ product.shopName || product.shop_name }}
                </div>
              </div>
              <!-- 新增标签显示 -->
              <div v-if="product.hot || product.newest || product.sale || product.specType" class="product-tags">
                <span v-if="product.hot" class="tag hot">热销</span>
                <span v-if="product.newest" class="tag newest">新品</span>
                <span v-if="product.sale" class="tag sale">促销</span>
                <span v-if="product.specType" class="tag spec">多规格</span>
              </div>
              <div class="product-actions">
                <div class="product-platform">{{ product.platform }}</div>
                <div class="action-buttons">
                  <a
                    :href="product.productLink || product.link"
                    target="_blank"
                    class="direct-link-btn"
                    title="直接打开商品页面"
                  >
                    直接打开
                  </a>
                  <button
                    @click="() => viewProductDetail(product)"
                    class="detail-btn"
                    title="查看详情"
                  >
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTabNavigation } from '../composables/useTabNavigation.js'
import api from '../api'

// 简单的消息提示函数
const showMessage = (type, text) => {
  alert(`${type}: ${text}`)
}

const stats = ref({
  recent_requests: 0,
  success_rate: 0,
  active_accounts: 0,
  total_platforms: 0
})

const platforms = ref([])

const searchForm = ref({
  platform: 'taobao',
  query: '',
  language: 'zh'
})

const searchResults = ref([])
const searching = ref(false)

const platformOptions = [
  { label: '淘宝', value: 'taobao' },
  { label: '1688', value: '1688' },
  { label: '拼多多', value: 'pinduoduo' },
  { label: '京东', value: 'jingdong' }
]

const languageOptions = [
  { label: '中文', value: 'zh' },
  { label: 'English', value: 'en' },
  { label: 'Français', value: 'fr' }
]

const loadStats = async () => {
  try {
    const response = await api.stats.getOverview()
    console.log("response",response)
    if (response.code === 200) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadPlatforms = async () => {
  try {
    const response = await api.platforms.getAll()
    if (response.code === 200) {
      platforms.value = response.data.map(p => ({
        code: p.code,
        name: p.name,
        status: p.status
      }))
    }
  } catch (error) {
    console.error('加载平台数据失败:', error)
  }
}

// 刷新数据
const refreshData = () => {
  loadStats()
  loadPlatforms()
}

const handleSearch = async () => {
  if (!searchForm.value.query.trim()) {
    showMessage('警告', '请输入搜索关键词')
    return
  }

  searching.value = true
  try {
    const response = await api.search.products(searchForm.value)
    console.log('搜索响应:', response)

    if (response.code === 200) {
      // 兼容新的数据格式：优先使用list，然后是products
      searchResults.value = response.data.list || response.data.products || []
      console.log('搜索结果:', searchResults.value)

      if (searchResults.value.length > 0) {
        showMessage('成功', `搜索完成，找到 ${response.data.total || searchResults.value.length} 个商品`)
      } else {
        showMessage('提示', '未找到相关商品')
      }
    } else {
      showMessage('错误', response.message || '搜索失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    showMessage('错误', '搜索失败，请稍后重试')
  } finally {
    searching.value = false
  }
}

const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150x150?text=图片加载失败'
}

// 在setup函数顶部获取router实例和TAB导航
const router = useRouter()
const { openProductDetail } = useTabNavigation()

const testRouterNavigation = () => {
  try {
    console.log('测试TAB导航')
    openProductDetail('https://item.taobao.com/item.htm?id=931163489227')
    console.log('测试TAB导航成功')
  } catch (error) {
    console.error('测试TAB导航失败:', error)
    showMessage('错误', 'TAB导航失败')
  }
}

const viewProductDetail = (product) => {
  try {
    console.log('点击查看详情，商品数据:', product)

    // 构建商品详情页面的URL参数，兼容新旧数据格式
    const url = product.productLink || product.link || `https://item.taobao.com/item.htm?id=${product.item_id || product.id || ''}`

    console.log('构建的URL:', url)

    // 使用TAB导航打开商品详情页面
    openProductDetail(url)

    console.log('TAB导航成功')
  } catch (error) {
    console.error('打开详情页面失败:', error)
    showMessage('错误', '打开详情页面失败')
  }
}

onMounted(() => {
  loadStats()
  loadPlatforms()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-secondary {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: normal;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin: 8px 0;
}

.stat-suffix {
  font-size: 12px;
  color: #999;
}

.search-card, .platforms-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}

.search-form {
  display: flex;
  gap: 16px;
  align-items: end;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item label {
  font-size: 14px;
  color: #666;
}

.form-item input, .form-item select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-btn {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn:hover {
  background: #40a9ff;
}

.search-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.search-results {
  margin-top: 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.product-card {
  display: flex;
  gap: 12px;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
  transition: box-shadow 0.2s;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-image {
  flex-shrink: 0;
}

.product-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.product-info {
  flex: 1;
}

.product-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.product-details {
  margin-bottom: 8px;
}

.product-sales,
.product-location,
.product-shop {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.product-sales {
  color: #52c41a;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
}

.product-platform {
  font-size: 12px;
  color: #1890ff;
  background: #f0f8ff;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.detail-btn {
  background: #52c41a;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.detail-btn:hover {
  background: #389e0d;
}

.direct-link-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  text-decoration: none;
  transition: background 0.2s;
  display: inline-block;
}

.direct-link-btn:hover {
  background: #40a9ff;
  color: white;
  text-decoration: none;
}

.market-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-left: 8px;
}

.product-tags {
  margin: 8px 0;
}

.tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  margin-right: 4px;
  margin-bottom: 2px;
}

.tag.hot {
  background: #ff4d4f;
  color: white;
}

.tag.newest {
  background: #52c41a;
  color: white;
}

.tag.sale {
  background: #fa8c16;
  color: white;
}

.tag.spec {
  background: #1890ff;
  color: white;
}

.platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.platform-card {
  text-align: center;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.platform-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.platform-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.platform-status.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.platform-status.developing {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}
</style>
